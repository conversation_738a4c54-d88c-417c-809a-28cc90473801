# Justfile for Tutlayt Rust project development environment
# This file configures a Docker development environment using colima, containerd, and nerdctl

# Default recipe to run when just is called without arguments
default:
    @just --list

# Setup colima with containerd and nerdctl
setup-colima:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "Setting up colima with containerd and nerdctl..."
    if ! command -v colima &> /dev/null; then
        echo "colima not found. Please install it with 'brew install colima'"
        exit 1
    fi
    
    # Check if colima is running, if not start it with containerd
    if ! colima status 2>/dev/null | grep -q "Running"; then
        echo "Starting colima with containerd runtime..."
        colima start --runtime containerd --cpu 4 --memory 8
    else
        echo "colima is already running"
    fi

# Build the dev container image
build-dev-container: setup-colima
    #!/usr/bin/env bash
    set -euo pipefail
    echo "Building custom code-server image with Rust and sccache..."
    nerdctl build -t tutlayt-code-server-rust -f Dockerfile.dev .
    echo "Creating necessary volumes..."
    nerdctl volume create tutlayt-code-server-config || true
    nerdctl volume create tutlayt-cargo-cache || true
    nerdctl volume create tutlayt-target-cache || true
    nerdctl volume create tutlayt-sccache-cache || true

# Start VS Code with the dev container
start-vscode: build-dev-container
    code --folder-uri vscode-remote://dev-container+${PWD}

# Run cargo commands inside the container
cargo *ARGS: setup-colima
    #!/usr/bin/env bash
    set -euo pipefail
    
    # Create target directory with proper permissions if it doesn't exist
    mkdir -p "${PWD}/target"
    chmod -R 777 "${PWD}/target"
    
    # Run cargo command in the container
    nerdctl run --rm -it \
        -v "${PWD}:/workspace" \
        -v tutlayt-cargo-cache:/usr/local/cargo \
        -v tutlayt-target-cache:/workspace/target \
        -v tutlayt-sccache-cache:/usr/local/cargo/sccache-cache \
        -w /workspace \
        --user rust \
        tutlayt-dev-container \
        bash -c "env && sccache --show-stats && cargo {{ARGS}}"

# Run cargo test inside the container
test: setup-colima
    @just cargo test

# Run cargo check inside the container
check: setup-colima
    @just cargo check

# Run cargo build inside the container
build: setup-colima
    @just cargo build

# Run cargo run inside the container
run *ARGS: setup-colima
    @just cargo run {{ARGS}}

# Check sccache statistics
sccache-stats: setup-colima
    #!/usr/bin/env bash
    set -euo pipefail
    
    nerdctl run --rm -it \
        -v "${PWD}:/workspace" \
        -v tutlayt-cargo-cache:/usr/local/cargo \
        -v tutlayt-target-cache:/workspace/target \
        -v tutlayt-sccache-cache:/usr/local/cargo/sccache-cache \
        -w /workspace \
        tutlayt-dev-container \
        sccache --show-stats

# Clean up resources
clean:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "Cleaning up resources..."
    
    # Remove container volumes
    nerdctl volume rm -f tutlayt-cargo-cache tutlayt-target-cache tutlayt-sccache-cache || true
    
    # Remove temporary files
    rm -rf .devcontainer

# Stop colima
stop-colima:
    colima stop

# Start a persistent dev container and open VS Code in the browser
start-dev: ## Start the persistent dev container
    #!/usr/bin/env bash
    set -euo pipefail
    echo "Starting persistent dev container..."
    nerdctl run -d --name tutlayt-dev \
        -e PUID=$(id -u) \
        -e PGID=$(id -g) \
        -e TZ=Etc/UTC \
        -e DEFAULT_WORKSPACE=/workspace \
        -p 8443:8443 \
        -v "$(pwd):/workspace:cached" \
        -v "tutlayt-code-server-config:/config" \
        -v "tutlayt-cargo-cache:/home/<USER>/.cargo/registry" \
        -v "tutlayt-target-cache:/workspace/target" \
        -v "tutlayt-sccache-cache:/home/<USER>/.cache/sccache" \
        --restart unless-stopped \
        tutlayt-code-server-rust
    echo "Persistent dev container started. Access VS Code at http://localhost:8443"

stop-dev: ## Stop and remove the persistent dev container
    #!/usr/bin/env bash
    set -euo pipefail
    echo "Stopping and removing persistent dev container..."
    CONTAINER_IDS=$(nerdctl ps -q -a --filter "name=tutlayt-dev")
    if [ -n "$CONTAINER_IDS" ]; then
        echo "Found containers to remove: $CONTAINER_IDS"
        for CONTAINER_ID in $CONTAINER_IDS; do
            echo "Stopping and removing container $CONTAINER_ID..."
            nerdctl rm -f "$CONTAINER_ID"
        done
        echo "All containers matching 'tutlayt-dev' stopped and removed."
    else
        echo "No containers found matching 'tutlayt-dev'."
    fi