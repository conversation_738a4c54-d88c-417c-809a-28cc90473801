# Cargo configuration for a production-focused workspace
# This file configures build optimizations, caching, and target-specific flags.

# --- General Build Configuration ---
[build]
# Use sccache for faster compilation by caching rustc invocations.
# Ensure sccache is installed and in your PATH.
rustc-wrapper = "sccache"

# --- Target-Specific Configurations ---
[target.x86_64-unknown-linux-gnu]
# Use lld linker on Linux for faster linking and potentially smaller/faster binaries.
rustflags = ["-C", "link-arg=-fuse-ld=lld"]

[target.x86_64-apple-darwin]
# Use default macOS linker.
rustflags = [] # No specific flags needed, defaults are generally good.

[target.aarch64-apple-darwin]
# Use default macOS linker for Apple Silicon.
rustflags = [] # No specific flags needed, defaults are generally good.

# --- Profile-Specific Configurations ---

# Development Profile: Optimized for fast compilation and easy debugging.
[profile.dev]
opt-level = 0        # No optimizations, fastest compile times.
debug = true         # Include debug information (same as `debug = 2`).
incremental = true   # Enable incremental compilation for faster subsequent builds.
panic = "unwind"     # Default, allows stack unwinding for better debugging.
# lto = false        # Default for dev
# codegen-units = 256 # Default for dev, many units for parallelism

# Release Profile: Optimized for maximum runtime performance and small binary size.
# This is what `cargo build --release` will use.
[profile.release]
opt-level = 3        # Enable all optimizations.
lto = "fat"          # Enable "fat" Link-Time Optimization for best performance/size.
                     # Alternatives: "thin" (faster LTO compile), false (no LTO).
codegen-units = 1    # Use a single codegen unit for maximum optimization potential with LTO.
                     # This significantly increases compile time.
panic = "abort"      # Abort on panic for smaller binaries and no unwinding overhead.
strip = "symbols"    # Strip symbols from the binary to reduce size.
                     # Alternatives: "debuginfo" (strips debuginfo but keeps symbols for backtraces),
                     # false (keeps everything).
debug = 0            # No debug information. (Equivalent to `debug = false`)
# incremental = false # Default for release

# Test Profile: Optimized for running tests.
# Inherits from `dev` by default, but we can customize.
[profile.test]
opt-level = 1        # Some optimizations, but not as aggressive as release.
                     # Balances test speed with compilation time.
                     # Use 0 for faster test compilation if test runtime isn't critical.
                     # Use 3 if precise performance of test code matters (like mini-benchmarks).
debug = true         # Include debug info for debugging tests.
incremental = true   # Faster recompilation of tests.
# panic = "unwind"     # Allow unwinding for better test failure messages.

# Benchmark Profile: Optimized for accurate benchmarking.
# Inherits from `release` by default.
[profile.bench]
opt-level = 3        # Maximize optimizations for accurate benchmark results.
lto = "fat"          # Use LTO for benchmarks.
codegen-units = 1    # Single codegen unit for best optimization.
debug = false        # No debug info needed for benchmarks.
# panic = "abort"    # Inherited from release if not specified.
# strip = "symbols"  # Inherited from release if not specified.

# Build Script Profile: Configuration for `build.rs` scripts.
[profile.build-override]
opt-level = 0        # Build scripts usually don't need heavy optimization.
# rustc-wrapper = "sccache" # This is not a standard Cargo key here.
                           # `sccache` for build scripts is handled if the build script
                           # itself invokes `rustc` and the global `rustc-wrapper` is set.
                           # If you were using `cc` crate, it might pick up `RUSTC_WRAPPER` from env.

# --- Notes ---
# 1. `sccache` for build scripts:
#    The global `[build] rustc-wrapper = "sccache"` will apply if a build script
#    invokes `rustc` (e.g., via the `cc` crate compiling C code).
#    There isn't a direct `[profile.build] rustc-wrapper` key.
#    The `[profile.build-override]` affects how the build script *itself* is compiled.
#
# 2. `Cargo.lock`: For truly reproducible production builds, always commit your `Cargo.lock` file.
#
# 3. `lto = "fat"` and `codegen-units = 1` for `release` and `bench`:
#    These settings provide the best optimization but significantly increase compilation times.
#    If build times are critical even for release (e.g., very frequent CI releases),
#    consider `lto = "thin"` or increasing `codegen-units`.
#
# 4. `strip = "symbols"` vs `strip = "debuginfo"`:
#    - "symbols": Removes most symbols. Smallest binary. Backtraces might be less informative.
#    - "debuginfo": Removes detailed DWARF/PDB debug info but keeps essential symbols for backtraces.
#      Slightly larger than "symbols". Choose based on your debugging needs for production crashes.