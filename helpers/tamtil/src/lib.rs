pub mod platform;
pub mod context;
pub mod actor;
pub mod memories;
pub mod messenger;
pub mod action_reaction;

// Re-export core types for easy access
pub use memories::{
    // Core types
    <PERSON><PERSON><PERSON><PERSON><PERSON>, TamtilKvError, MemoriesHandle,
    // Action-Reaction traits
    MemoryAction, MemoryReaction,
    // Actions
    RememberAction, RecallAction, ForgetAction, BatchCreateAction, CompactAction, ListAction,
    // Reactions
    MemoryStoredReaction, MemoryRecalledReaction, MemoryForgottenReaction,
    BatchCreatedReaction, CompactedReaction, ListedReaction,
    // Legacy compatibility
    EsKvHandle, StoreConfig, CleanupPolicy, DurabilityLevel, UserProfile
};

//=============================================================================
// ## Integration Tests - Demonstrating Seamless Collaboration
//=============================================================================

#[cfg(test)]
mod integration_tests {
    use super::*;
    use std::sync::Arc;
    use tempfile::TempDir;

    /// Example actor that demonstrates the action-reaction pattern
    struct TestActor {
        name: String,
    }

    #[async_trait::async_trait]
    impl context::Actor for TestActor {
        async fn handle_action(&self, action: context::ActionMessage, context: context::ActorContext) -> TamtilResult<context::ReactionMessage> {
            match action.action_type.as_str() {
                "Greet" => {
                    let greeting = format!("Hello from {}!", self.name);

                    // Store the greeting in memories
                    context.remember("last_greeting", greeting.as_bytes().to_vec()).await?;

                    Ok(context::ReactionMessage {
                        action_id: action.action_id,
                        reaction_type: "GreetingGenerated".to_string(),
                        payload: greeting.into_bytes(),
                        timestamp: std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap()
                            .as_secs(),
                        success: true,
                        error_message: None,
                    })
                }
                "GetLastGreeting" => {
                    match context.recall("last_greeting").await? {
                        Some(data) => {
                            Ok(context::ReactionMessage {
                                action_id: action.action_id,
                                reaction_type: "LastGreetingRetrieved".to_string(),
                                payload: data,
                                timestamp: std::time::SystemTime::now()
                                    .duration_since(std::time::UNIX_EPOCH)
                                    .unwrap()
                                    .as_secs(),
                                success: true,
                                error_message: None,
                            })
                        }
                        None => {
                            Ok(context::ReactionMessage {
                                action_id: action.action_id,
                                reaction_type: "NoGreetingFound".to_string(),
                                payload: b"No greeting found".to_vec(),
                                timestamp: std::time::SystemTime::now()
                                    .duration_since(std::time::UNIX_EPOCH)
                                    .unwrap()
                                    .as_secs(),
                                success: false,
                                error_message: Some("No greeting found".to_string()),
                            })
                        }
                    }
                }
                _ => {
                    Ok(context::ReactionMessage {
                        action_id: action.action_id,
                        reaction_type: "UnknownAction".to_string(),
                        payload: b"Unknown action type".to_vec(),
                        timestamp: std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap()
                            .as_secs(),
                        success: false,
                        error_message: Some("Unknown action type".to_string()),
                    })
                }
            }
        }

        fn actor_type(&self) -> &'static str {
            "TestActor"
        }
    }

    /// Helper to create a test platform with all components
    async fn create_test_platform() -> TamtilResult<(platform::PlatformHandle, TempDir)> {
        let temp_dir = tempfile::tempdir().unwrap();
        let config = platform::PlatformConfig {
            data_path: temp_dir.path().to_path_buf(),
            enable_debug_logging: true,
            ..Default::default()
        };
        let platform = platform::PlatformHandle::new(config)?;
        Ok((platform, temp_dir))
    }

    #[tokio::test]
    async fn test_complete_tamtil_integration() {
        // Arrange: Create platform with all components
        let (platform, _temp_dir) = create_test_platform().await.unwrap();

        // Act: Start platform (this starts memories and messenger)
        let platform_info = platform.start().await.unwrap();
        println!("✅ Platform started: {}", platform_info.platform_id);

        // Act: Get platform services
        let memories = platform.get_kv_store().await.unwrap();
        let messenger = platform.get_messenger().await.unwrap();

        // Act: Create a context
        let context_config = context::ContextConfig {
            context_id: "test_context".to_string(),
            enable_debug_logging: true,
            ..Default::default()
        };
        let context_handle = context::ContextHandle::new(context_config, memories.clone(), messenger.clone()).unwrap();

        // Act: Register the context with messenger for network routing
        messenger.register_context("test_context".to_string(), context_handle.clone()).await.unwrap();
        println!("✅ Context registered with messenger");

        // Act: Create and register a test actor
        let test_actor = Arc::new(TestActor {
            name: "TestBot".to_string(),
        });

        let actor_registration = context::ActorBuilder::new("test_context", "TestActor")
            .instance_id("test_instance_1")
            .timeout(std::time::Duration::from_secs(5))
            .metadata("version", "1.0")
            .build(test_actor);

        let actor_id = context_handle.register_actor(actor_registration).await.unwrap();
        println!("✅ Actor registered: {}", actor_id.to_string());

        // Act: Send an action to the actor
        let greet_action = context::ActionMessage {
            action_id: "action_001".to_string(),
            action_type: "Greet".to_string(),
            payload: b"Hello World".to_vec(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            correlation_id: Some("test_correlation".to_string()),
        };

        let reaction = context_handle.send_action(actor_id.clone(), greet_action).await.unwrap();
        println!("✅ Action processed, reaction: {}", reaction.reaction_type);

        // Assert: Verify the reaction
        assert_eq!(reaction.reaction_type, "GreetingGenerated");
        assert!(reaction.success);
        let greeting = String::from_utf8(reaction.payload).unwrap();
        assert_eq!(greeting, "Hello from TestBot!");

        // Act: Send another action to retrieve the stored greeting
        let get_greeting_action = context::ActionMessage {
            action_id: "action_002".to_string(),
            action_type: "GetLastGreeting".to_string(),
            payload: Vec::new(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            correlation_id: Some("test_correlation_2".to_string()),
        };

        let reaction2 = context_handle.send_action(actor_id.clone(), get_greeting_action).await.unwrap();
        println!("✅ Second action processed, reaction: {}", reaction2.reaction_type);

        // Assert: Verify the stored greeting was retrieved
        assert_eq!(reaction2.reaction_type, "LastGreetingRetrieved");
        assert!(reaction2.success);
        let stored_greeting = String::from_utf8(reaction2.payload).unwrap();
        assert_eq!(stored_greeting, "Hello from TestBot!");

        // Act: Test direct memories access
        let test_key = "integration_test_key".to_string();
        let test_value = b"integration_test_value".to_vec();
        memories.create(test_key.clone(), test_value.clone()).await.unwrap();

        let retrieved_value = memories.get(test_key).await.unwrap().unwrap();
        assert_eq!(&*retrieved_value, &test_value);
        println!("✅ Direct memories access working");

        // Act: Test messenger statistics
        let messenger_stats = messenger.get_stats().await.unwrap();
        println!("✅ Messenger stats: {} messages sent", messenger_stats.messages_sent);

        // Act: Test context statistics
        let context_stats = context_handle.get_stats().await.unwrap();
        println!("✅ Context stats: {} actions processed", context_stats.actions_processed);
        assert_eq!(context_stats.actions_processed, 2);
        assert_eq!(context_stats.actor_count, 1);

        // Act: List actors
        let actors = context_handle.list_actors().await.unwrap();
        assert_eq!(actors.len(), 1);
        assert_eq!(actors[0], actor_id);
        println!("✅ Actor listing working");

        // Act: Unregister actor
        context_handle.unregister_actor(actor_id.clone()).await.unwrap();
        println!("✅ Actor unregistered");

        // Act: Verify actor is gone
        let actors_after = context_handle.list_actors().await.unwrap();
        assert_eq!(actors_after.len(), 0);

        // Act: Shutdown everything
        context_handle.shutdown().await.unwrap();
        messenger.shutdown().await.unwrap();
        platform.shutdown().await.unwrap();
        println!("✅ All components shut down successfully");

        println!("\n🎉 COMPLETE TAMTIL INTEGRATION TEST PASSED!");
        println!("✅ Platform -> Context -> Actor -> Reaction -> Memories flow working");
        println!("✅ Messenger integration working");
        println!("✅ Action-reaction pattern implemented correctly");
        println!("✅ Event-sourced memories working");
        println!("✅ Alice Ryhl's actor pattern followed");
    }
}

