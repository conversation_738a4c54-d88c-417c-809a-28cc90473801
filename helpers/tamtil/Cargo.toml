[package]
name = "tamtil"
version = "0.1.0"
edition.workspace = true
description = "TAMTIL - Distributed Actor system"
license = "MIT"

[features]
default = []
debug = ["tracing-subscriber"]

[dependencies]
memmap2        = {workspace = true}
rkyv           = { workspace = true, features = ["std", "alloc", "hashbrown-0_15"] }
rkyv_derive    = {workspace = true}
thiserror      = {workspace = true}
tokio          = { workspace = true , features = ["full"] }
seahash        = {workspace = true}
tracing        = { workspace = true }
tracing-subscriber = { workspace = true, optional = true }
quinn = { workspace = true }
rustls = { workspace = true }
rustls-pemfile.workspace = true
rcgen.workspace = true
async-trait.workspace = true
bytes.workspace = true
uuid = { version = "1.0", features = ["v4"] }
[dev-dependencies]
criterion = { workspace = true, features = ["html_reports"] }
tempfile.workspace = true
futures.workspace = true

[[bench]]
name = "kv_store_bench"
harness = false