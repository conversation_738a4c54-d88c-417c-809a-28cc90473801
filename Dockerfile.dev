# Use linuxserver/code-server as the base image
FROM lscr.io/linuxserver/code-server:latest

# Set environment variables for non-interactive installation
ENV DEBIAN_FRONTEND=noninteractive

# Switch to root user to install packages
USER root

# Install Rust, build essentials, and other dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    pkg-config \
    libssl-dev \
    git \
    # Rust installation dependencies
    ca-certificates \
    gnupg \
    && rm -rf /var/lib/apt/lists/*

# Install Rustup (official Rust installer)
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --default-toolchain stable

# Add Rust to PATH
ENV PATH="/config/.cargo/bin:${PATH}"

# Install sccache using cargo
RUN /config/.cargo/bin/cargo install sccache

# Set environment variables for sccache
ENV RUSTC_WRAPPER="/config/.cargo/bin/sccache"
ENV SCCACHE_DIR="/config/.cache/sccache"
# Set to debug or trace for more verbose logs
ENV SCCACHE_LOG=error

# Create sccache directory and set permissions (as the 'abc' user, which linuxserver images use)
# The PUID/PGID environment variables passed to `docker run` will determine the 'abc' user's actual UID/GID.
RUN mkdir -p /config/.cache/sccache && \
    chown -R abc:abc /config/.cache/sccache && \
    chmod -R 777 /config/.cache/sccache && \
    mkdir -p /config/.cargo && \
    chown -R abc:abc /config/.cargo && \
    chmod -R 777 /config/.cargo

# Switch back to the default user for the linuxserver/code-server image (usually abc)
USER abc

# Set home for cargo operations if not already set by base image for user abc
ENV CARGO_HOME=/config/.cargo

# Verify installations
RUN echo "Rust version:" && rustc --version && \
    echo "Cargo version:" && cargo --version && \
    echo "sccache version:" && sccache --version && \
    echo "PATH: ${PATH}" && \
    echo "RUSTC_WRAPPER: ${RUSTC_WRAPPER}" && \
    echo "SCCACHE_DIR: ${SCCACHE_DIR}"