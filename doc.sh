#!/bin/bash

# A script to generate rkyv documentation, extract all text content,
# filter out noise, and concatenate it into a single line for analysis.
# This version uses robust tools to handle character encodings and redirects.

# --- Environment Configuration ---
# Set a UTF-8 locale to handle all characters correctly. This is critical.
# If 'C.UTF-8' is not available on your system, try 'en_US.UTF-8'.
# You can check available locales with the command `locale -a`.
export LC_ALL=C.UTF-8

# --- Configuration ---
CRATE_NAME="rkyv"
OUTPUT_FILE="rkyv_docs_full.txt"

# --- Pre-flight Checks ---
if ! command -v cargo &> /dev/null; then
    echo "Error: 'cargo' command not found. Please ensure the Rust toolchain is in your PATH." >&2
    exit 1
fi

if ! command -v lynx &> /dev/null; then
    echo "Error: 'lynx' command not found. It is required to extract text from HTML." >&2
    echo "Please install it using your package manager (e.g., 'sudo apt-get install lynx' or 'brew install lynx')." >&2
    exit 1
fi

if [ ! -f "Cargo.toml" ]; then
    echo "Warning: No Cargo.toml found in the current directory." >&2
    echo "Running 'cargo doc' might fail if not executed from the workspace root." >&2
fi


# --- Main Script ---

# 1. Clean up previous output file
echo "Cleaning up old output file..."
rm -f "$OUTPUT_FILE"

# 2. Generate the documentation for the rkyv package within the workspace
echo "Generating local documentation for '$CRATE_NAME'..."
cargo doc --no-deps --package "$CRATE_NAME" || { echo "Error: 'cargo doc' failed." >&2; exit 1; }
echo "Documentation generated successfully."


# 3. Find, convert, filter, and concatenate all generated HTML files into a SINGLE LINE
echo "Assembling all documentation text into a single line in: $OUTPUT_FILE..."

CRATE_PATH_NAME=$(echo "$CRATE_NAME" | tr '-' '_')
DOCS_PATH="target/doc/$CRATE_PATH_NAME"

if [ ! -d "$DOCS_PATH" ]; then
    echo "Error: Could not find the generated documentation directory at '$DOCS_PATH'." >&2
    echo "Please check the 'target/doc/' directory for the correct path." >&2
    exit 1
fi

# This pipeline is now more robust:
# 1. 'find' and 'while' loop through files.
# 2. 'lynx' extracts text.
# 3. 'grep -v' filters out "Redirecting" lines from lynx.
# 4. 'awk' joins all lines into a single line, which is safer than 'tr' for this task.
# 5. 'tr -s' cleans up any extra spaces.
find "$DOCS_PATH" -type f -name "*.html" | sort | while read -r filepath; do
    lynx -dump -nolist -stdin < "$filepath"
done | grep -v "Redirecting$" | awk '{printf "%s ", $0}' | tr -s ' ' > "$OUTPUT_FILE"

# Add a final newline character to the end of the file for POSIX compliance.
echo "" >> "$OUTPUT_FILE"


# --- Done ---
echo "Success! All documentation text for '$CRATE_NAME' saved as a single line to '$OUTPUT_FILE'."
echo "Please provide the contents of this file for analysis."